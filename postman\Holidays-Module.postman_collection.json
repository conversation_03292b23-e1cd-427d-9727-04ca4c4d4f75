{"info": {"_postman_id": "holidays-module-collection", "name": "Finework API - Holidays Module", "description": "Holiday management endpoints for Finework API with full CRUD operations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Holidays", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/holidays?page={{page}}&limit={{limit}}", "host": ["{{baseUrl}}"], "path": ["holidays"], "query": [{"key": "page", "value": "{{page}}", "description": "Page number for pagination"}, {"key": "limit", "value": "{{limit}}", "description": "Number of items per page"}]}, "description": "Retrieve paginated list of all holidays, ordered by date descending"}, "response": []}, {"name": "Get Holiday by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/holidays/{{holiday_id}}", "host": ["{{baseUrl}}"], "path": ["holidays", "{{holiday_id}}"]}, "description": "Retrieve a specific holiday by their ID"}, "response": []}, {"name": "Create Holiday", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Year's Day\",\n  \"date\": \"2024-01-01T00:00:00Z\",\n  \"is_national\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/holidays", "host": ["{{baseUrl}}"], "path": ["holidays"]}, "description": "Create a new holiday. Required fields: name, date. is_national defaults to false if not provided."}, "response": []}, {"name": "Update Holiday", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Year's Day (Updated)\",\n  \"date\": \"2024-01-01T00:00:00Z\",\n  \"is_national\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/holidays/{{holiday_id}}", "host": ["{{baseUrl}}"], "path": ["holidays", "{{holiday_id}}"]}, "description": "Update an existing holiday. All fields are optional for updates."}, "response": []}, {"name": "Delete Holiday", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/holidays/{{holiday_id}}", "host": ["{{baseUrl}}"], "path": ["holidays", "{{holiday_id}}"]}, "description": "Delete a holiday by their ID"}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "YOUR_AUTH_TOKEN_HERE", "type": "string"}, {"key": "page", "value": "1", "type": "string"}, {"key": "limit", "value": "10", "type": "string"}, {"key": "holiday_id", "value": "HOLIDAY_ID_HERE", "type": "string"}]}