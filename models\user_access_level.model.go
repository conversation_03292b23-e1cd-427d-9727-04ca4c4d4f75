package models

import "time"

type UserPermissionLevel string

const (
	UserPermissionLevelNone  UserPermissionLevel = "NONE"
	UserPermissionLevelUser  UserPermissionLevel = "USER"
	UserPermissionLevelAdmin UserPermissionLevel = "ADMIN"
	UserPermissionLevelSuper UserPermissionLevel = "SUPER"
)

type UserAccessLevel struct {
	UserID    string              `json:"user_id" gorm:"column:user_id;uniqueIndex;type:uuid"`
	Clockin   UserPermissionLevel `json:"clockin" gorm:"column:clockin"`
	Timesheet UserPermissionLevel `json:"timesheet" gorm:"column:timesheet"`
	Pmo       UserPermissionLevel `json:"pmo" gorm:"column:pmo"`
	Setting   UserPermissionLevel `json:"setting" gorm:"column:setting"`
	CreatedAt *time.Time          `json:"created_at" gorm:"column:created_at"`
	UpdatedAt *time.Time          `json:"updated_at" gorm:"column:updated_at"`

	// Relations
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID;constraint:OnDelete:CASCADE"`
}

func (UserAccessLevel) TableName() string {
	return "user_access_levels"
}
