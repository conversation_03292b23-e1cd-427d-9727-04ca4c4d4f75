package models

type Ministry struct {
	BaseModel
	NameTh string  `json:"name_th" gorm:"column:name_th"`
	NameEn *string `json:"name_en" gorm:"column:name_en"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Departments []Department `json:"departments,omitempty" gorm:"foreignKey:MinistryID;references:ID"`
}

func (Ministry) TableName() string {
	return "ministries"
}
