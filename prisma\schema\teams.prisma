model teams {
  id               String    @id @default(uuid()) @db.Uuid
  name             String    @unique
  code             String    @unique
  description      String?
  color            String    @default("")
  working_start_at DateTime? @db.Time()
  working_end_at   DateTime? @db.Time()

  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  // Relations
  users users[]

  @@index([name, code])
}
