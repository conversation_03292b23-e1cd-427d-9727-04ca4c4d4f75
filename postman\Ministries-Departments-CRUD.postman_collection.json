{"info": {"_postman_id": "ministries-departments-crud", "name": "Ministries & Departments CRUD", "description": "Complete CRUD operations for Ministries and Departments in Finework API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Ministries", "item": [{"name": "Get All Ministries", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/ministries?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["ministries"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Ministry by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/ministries/{{ministryId}}", "host": ["{{baseUrl}}"], "path": ["ministries", "{{ministryId}}"]}}}, {"name": "Create Ministry (Admin Only)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{superAdminToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name_th\": \"กระทรวงการคลัง\",\n  \"name_en\": \"Ministry of Finance\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/ministries", "host": ["{{baseUrl}}"], "path": ["admin", "ministries"]}}}, {"name": "Update Ministry (Admin Only)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{superAdminToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name_th\": \"กระทรวงการคลัง (อัปเดต)\",\n  \"name_en\": \"Ministry of Finance (Updated)\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/ministries/{{ministryId}}", "host": ["{{baseUrl}}"], "path": ["admin", "ministries", "{{ministryId}}"]}}}, {"name": "Delete Ministry (Admin Only)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{superAdminToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/ministries/{{ministryId}}", "host": ["{{baseUrl}}"], "path": ["admin", "ministries", "{{ministryId}}"]}}}]}, {"name": "Departments", "item": [{"name": "Get All Departments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/departments?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["departments"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Department by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/departments/{{departmentId}}", "host": ["{{baseUrl}}"], "path": ["departments", "{{departmentId}}"]}}}, {"name": "Create Department (Admin Only)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{superAdminToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ministry_id\": \"{{ministryId}}\",\n  \"name_th\": \"กรมบัญชีกลาง\",\n  \"name_en\": \"Comptroller General's Department\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/departments", "host": ["{{baseUrl}}"], "path": ["admin", "departments"]}}}, {"name": "Update Department (Admin Only)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{superAdminToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ministry_id\": \"{{ministryId}}\",\n  \"name_th\": \"กรมบัญชีกลาง (อัปเดต)\",\n  \"name_en\": \"Comptroller General's Department (Updated)\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/departments/{{departmentId}}", "host": ["{{baseUrl}}"], "path": ["admin", "departments", "{{departmentId}}"]}}}, {"name": "Delete Department (Admin Only)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{superAdminToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/departments/{{departmentId}}", "host": ["{{baseUrl}}"], "path": ["admin", "departments", "{{departmentId}}"]}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "YOUR_AUTH_TOKEN_HERE", "type": "string"}, {"key": "superAdminToken", "value": "YOUR_SUPER_ADMIN_TOKEN_HERE", "type": "string"}, {"key": "ministryId", "value": "MINISTRY_ID_HERE", "type": "string"}, {"key": "departmentId", "value": "DEPARTMENT_ID_HERE", "type": "string"}]}