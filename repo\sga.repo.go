package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var Sga = repository.Make[models.Sga]()

func SgaOrderBy(pageOptions *core.PageOptions) repository.Option[models.Sga] {
	return func(c repository.IRepository[models.Sga]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func SgaWithSearch(q string) repository.Option[models.Sga] {
	return func(c repository.IRepository[models.Sga]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name ILIKE ?", searchTerm)
	}
}
