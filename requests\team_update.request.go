package requests

import (
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
)

type TeamUpdate struct {
	core.BaseValidator
	Name           *string `json:"name"`
	Code           *string `json:"code"`
	Color          *string `json:"color"`
	Description    *string `json:"description"`
	StartWorkingAt *string `json:"working_start_at"`
	EndWorkingAt   *string `json:"working_end_at"`
}

func (r *TeamUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldCode := ""
	oldName := ""
	team, _ := repo.Team(cc).FindOne("id = ?", cc.Param("id"))
	if team != nil {
		oldCode = team.Code
		oldName = team.Name
	}

	r.Must(r.<PERSON>nique(ctx, r.Name, models.Team{}.TableName(), "name", oldName, "name"))
	r.Must(r.<PERSON>(ctx, r.Code, models.Team{}.TableName(), "code", oldCode, "code"))

	if r.StartWorkingAt != nil {
		if _, err := time.Parse("15:04", *r.StartWorkingAt); err != nil {
			r.Must(false, &core.IValidMessage{
				Name:    "working_start_at",
				Code:    "INVALID_TIME_FORMAT",
				Message: "Start working time must be in HH:MM format",
			})
		}
	}

	if r.EndWorkingAt != nil {
		if _, err := time.Parse("15:04", *r.EndWorkingAt); err != nil {
			r.Must(false, &core.IValidMessage{
				Name:    "working_end_at",
				Code:    "INVALID_TIME_FORMAT",
				Message: "End working time must be in HH:MM format",
			})
		}
	}

	// Validate working hours if both are provided
	if r.StartWorkingAt != nil && r.EndWorkingAt != nil {
		startTime, err1 := time.Parse("15:04", *r.StartWorkingAt)
		endTime, err2 := time.Parse("15:04", *r.EndWorkingAt)

		if err1 == nil && err2 == nil {
			if startTime.After(endTime) || startTime.Equal(endTime) {
				r.Must(false, &core.IValidMessage{
					Name:    "working_start_at",
					Code:    "INVALID_WORKING_HOURS",
					Message: "Start working time must be before end working time",
				})
			}
		}
	}

	return r.Error()
}
