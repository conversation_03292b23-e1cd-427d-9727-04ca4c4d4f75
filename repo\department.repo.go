package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var Department = repository.Make[models.Department]()

func DepartmentOrderBy(pageOptions *core.PageOptions) repository.Option[models.Department] {
	return func(c repository.IRepository[models.Department]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name_th ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func DepartmentWithMinistry() repository.Option[models.Department] {
	return func(c repository.IRepository[models.Department]) {
		c.Preload("Ministry")
	}
}

func DepartmentWithSearch(q string) repository.Option[models.Department] {
	return func(c repository.IRepository[models.Department]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name_th ILIKE ? OR name_en ILIKE ?", searchTerm, searchTerm)
	}
}

func DepartmentWithMinistryFilter(ministryID *string) repository.Option[models.Department] {
	return func(c repository.IRepository[models.Department]) {
		if ministryID == nil {
			return
		}
		c.Where("ministry_id = ?", *ministryID)
	}
}
