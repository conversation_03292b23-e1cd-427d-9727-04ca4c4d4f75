### Update User Access Level API Test
### This file tests the UpdateAccessLevel API endpoint

### 1. Update all access levels for a user
PUT http://localhost:3000/users/{{user_id}}/access_level
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "clockin": "ADMIN",
  "timesheet": "USER",
  "pmo": "SUPER",
  "setting": "NONE"
}

### 2. Update only specific access levels (partial update)
PUT http://localhost:3000/users/{{user_id}}/access_level
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "clockin": "USER",
  "pmo": "ADMIN"
}

### 3. Test with invalid permission level (should return validation error)
PUT http://localhost:3000/users/{{user_id}}/access_level
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "clockin": "INVALID_LEVEL",
  "timesheet": "USER"
}

### 4. Get user to verify access level changes
GET http://localhost:3000/users/{{user_id}}
Authorization: Bearer {{token}}

### Valid permission levels:
### - NONE: No access
### - USER: Basic user access
### - ADMIN: Administrative access
### - SUPER: Super admin access
###
### Access level modules:
### - clockin: Clock-in/check-in functionality
### - timesheet: Timesheet management
### - pmo: Project Management Office features
### - setting: System settings access
