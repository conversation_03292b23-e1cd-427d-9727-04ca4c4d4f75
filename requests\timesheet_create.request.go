package requests

import (
	"fmt"
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type TimesheetCreateItem struct {
	ProjectCode *string  `json:"project_code"`
	SgaID       *string  `json:"sga_id"`
	Timing      *float64 `json:"timing"`
	Type        *string  `json:"type"`
	LeaveType   *string  `json:"leave_type"`
	Description *string  `json:"description"`
	Date        *string  `json:"date"`

	// Relations
	ProjectID   *string `json:"project_id"`
	ProjectName *string `json:"project_name"`
	SgaName     *string `json:"sga_name"`
}

type TimesheetCreate struct {
	core.BaseValidator
	Items []TimesheetCreateItem `json:"items"`
}

func (r *TimesheetCreate) Valid(ctx core.IContext) core.IError {
	// Validate that items array is not empty
	if r.Must(r.<PERSON>quired(r.Items, "items")) && r.Must(r.<PERSON>(r.Items, 1, "items")) {
		// Validate each item in the array
		for i, item := range r.Items {
			fieldPrefix := fmt.Sprintf("items[%d]", i)

			// Validate type
			r.Must(r.IsStrIn(item.Type, strings.Join([]string{string(models.TimesheetTypeProject), string(models.TimesheetTypeSga),
				string(models.TimesheetTypeLeave), string(models.TimesheetTypeInternal),
				string(models.TimesheetTypeExternal), string(models.TimesheetTypeOt)}, "|"), fieldPrefix+".type"))

			if r.Must(r.IsStrRequired(item.Type, fieldPrefix+".type")) {
				switch utils.ToNonPointer(item.Type) {
				case string(models.TimesheetTypeLeave):
					r.Items[i].ProjectCode = nil
					r.Items[i].SgaID = nil
					r.Must(r.IsStrRequired(item.LeaveType, fieldPrefix+".leave_type"))
					r.Must(r.IsStrIn(item.LeaveType, strings.Join([]string{
						string(models.CheckinLeaveTypeAnnual),
						string(models.CheckinLeaveTypeSick),
						string(models.CheckinLeaveTypeBusiness),
						string(models.CheckinLeaveTypeMenstrual),
						string(models.CheckinLeaveTypeBirthday),
						string(models.CheckinLeaveTypeOrdination),
					}, "|"), fieldPrefix+".leave_type"))
				case string(models.TimesheetTypeProject):
					r.Items[i].SgaID = nil
					r.Items[i].LeaveType = nil
					r.Must(r.IsStrRequired(item.ProjectCode, fieldPrefix+".project_code"))
				case string(models.TimesheetTypeSga):
					r.Items[i].ProjectCode = nil
					r.Items[i].LeaveType = nil
					r.Must(r.IsStrRequired(item.SgaID, fieldPrefix+".sga_id"))
				case string(models.TimesheetTypeInternal), string(models.TimesheetTypeExternal):
					r.Items[i].ProjectCode = nil
					r.Items[i].SgaID = nil
					r.Items[i].LeaveType = nil
				}

			}

			// Validate date
			if r.Must(r.IsRequired(item.Date, fieldPrefix+".date")) {
				r.Must(r.IsDate(item.Date, fieldPrefix+".date"))
			}

			// Validate timing
			r.Must(r.IsRequired(item.Timing, fieldPrefix+".timing"))

			if item.ProjectCode != nil {
				project, _ := repo.Project(ctx).FindOne("code = ?", item.ProjectCode)
				if project != nil {
					r.Items[i].ProjectID = &project.ID
					r.Items[i].ProjectName = &project.Name
				}
			}

			if item.SgaID != nil {
				sga, _ := repo.Sga(ctx).FindOne("id = ?", item.SgaID)
				if sga != nil {
					r.Items[i].SgaName = &sga.Name
				}
			}
		}
	}

	return r.Error()
}
