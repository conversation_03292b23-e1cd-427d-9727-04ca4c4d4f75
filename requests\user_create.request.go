package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type UserCreate struct {
	core.BaseValidator
	Email       *string `json:"email"`
	FullName    *string `json:"full_name"`
	DisplayName *string `json:"display_name"`
	Position    *string `json:"position"`
	TeamCode    *string `json:"team_code"`
	AvatarURL   *string `json:"avatar_url"`
}

func (r *UserCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsEmail(r.Email, "email"))
	r.Must(r.IsStrRequired(r.Email, "email"))
	r.Must(r.<PERSON>(ctx, r.Email, models.User{}.TableName(), "email", "", "email"))

	r.Must(r.IsStrRequired(r.FullName, "full_name"))

	r.Must(r.IsExists(ctx, r.TeamCode, models.Team{}.TableName(), "code", "team_code"))
	r.<PERSON>(r.<PERSON>(r.AvatarURL, "avatar_url"))
	return r.<PERSON>()
}
