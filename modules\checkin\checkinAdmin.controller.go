package checkin

import (
	"net/http"
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type CheckinAdminController struct {
}

func (m CheckinAdminController) Pagination(c core.IHTTPContext) error {
	input := &requests.CheckinPaginationRequest{}
	if err := c.Bind(input); err != nil {
		ierr := emsgs.InvalidParamsError
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	teamCode := []string{}
	if input.TeamCode != nil {
		teamCode = strings.Split(utils.ToNonPointer(input.TeamCode), ",")
	}

	checkinSvc := services.NewCheckinService(c)
	res, ierr := checkinSvc.Pagination(c.GetPageOptions(), &services.CheckinPaginationOptions{
		UserID:    input.UserID,
		StartDate: input.StartDate,
		EndDate:   input.EndDate,
		TeamCode:  teamCode,
		Type:      input.Type,
		IsUnused:  input.IsUnused,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}
	return c.JSON(http.StatusOK, res)
}

func (m CheckinAdminController) Find(c core.IHTTPContext) error {
	checkinSvc := services.NewCheckinService(c)
	checkin, err := checkinSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, checkin)
}
