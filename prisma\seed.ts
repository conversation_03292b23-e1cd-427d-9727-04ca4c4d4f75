import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();
async function main() {
//   const alice = await prisma.user.create({
//     data: { email: "<EMAIL>", name: "<PERSON>", age: 18 },
//   });
//   console.log({ alice });
}
main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
