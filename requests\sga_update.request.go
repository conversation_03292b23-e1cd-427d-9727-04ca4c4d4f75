package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
)

type SgaUpdate struct {
	core.BaseValidator
	Name        *string `json:"name"`
	Description *string `json:"description"`
}

func (r *SgaUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldName := ""
	sga, _ := repo.Sga(cc).FindOne("id = ?", cc.Param("id"))
	if sga != nil {
		oldName = sga.Name
	}

	r.Must(r.<PERSON>tr<PERSON>(ctx, r.Name, models.Sga{}.TableName(), "name", oldName, "name"))

	return r.Error()
}
