package services

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"
	"strings"

	"github.com/golang-jwt/jwt"
	"github.com/slack-go/slack"
	"gitlab.finema.co/finema/finework/finework-api/consts"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

type IAuthService interface {
	Logout(token string) core.IError
	SlackCallback(input *SlackCallbackPayload) (*LoginResponse, core.IError)
	GenerateToken() (string, error)
	ValidateToken(token string) (*models.User, core.IError)
	CreateUserToken(userID, token, ipAddress, userAgent, deviceInfo string) core.IError
	DeleteUserToken(token string) core.IError
}

type authService struct {
	ctx core.IContext
}

type SlackCallbackPayload struct {
	Code       string
	State      string
	IPAddress  string
	UserAgent  string
	DeviceInfo string
}

type LoginResponse struct {
	*models.User
	Token string `json:"token"`
}

func (s authService) Logout(token string) core.IError {
	return s.DeleteUserToken(token)
}

func (s authService) SlackCallback(input *SlackCallbackPayload) (*LoginResponse, core.IError) {
	// Exchange authorization code for access token
	slackUser, ierr := s.exchangeSlackCode(input.Code)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Find or create user based on Slack email
	user, ierr := s.findOrCreateSlackUser(slackUser)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Generate token
	token, err := s.GenerateToken()
	if err != nil {
		ierr = core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "FAILED_TO_GENERATE_TOKEN",
			Message: "Failed to generate token",
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	// Create user token record
	ierr = s.CreateUserToken(user.ID, token, input.IPAddress, input.UserAgent, input.DeviceInfo)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return &LoginResponse{
		User:  user,
		Token: token,
	}, nil
}

func (s authService) GenerateToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func (s authService) ValidateToken(token string) (*models.User, core.IError) {
	userToken, ierr := repo.UserToken(s.ctx).FindOne("token = ?", token)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	user, ierr := repo.User(s.ctx).FindOne("id = ?", userToken.UserID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return user, nil
}

func (s authService) CreateUserToken(userID, token, ipAddress, userAgent, deviceInfo string) core.IError {
	userToken := &models.UserToken{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		UserID:              userID,
		Token:               token,
		IPAddress:           ipAddress,
		UserAgent:           userAgent,
		DeviceInfo:          deviceInfo,
	}

	return repo.UserToken(s.ctx).Create(userToken)
}

func (s authService) DeleteUserToken(token string) core.IError {
	return repo.UserToken(s.ctx).Delete("token = ?", token)
}

// SlackUser represents the user data from Slack OAuth
type SlackUser struct {
	ID          string `json:"id"`
	Email       string `json:"email"`
	DisplayName string `json:"display_name"`
	FullName    string `json:"full_name"`
	Image       string `json:"image"`
	Company     string `json:"company"`
}

func (s authService) exchangeSlackCode(code string) (*SlackUser, core.IError) {
	// Get Slack OAuth configuration from environment
	clientID := s.ctx.ENV().String(consts.EnvSlackClientID)
	clientSecret := s.ctx.ENV().String(consts.EnvSlackSecret)
	redirectURL := strings.Replace(s.ctx.ENV().String(consts.EnvAPIURL)+"/auth/slack-callback", "http://", "https://", 1)

	if clientID == "" || clientSecret == "" {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_CONFIG_MISSING",
			Message: "Slack OAuth configuration is missing",
		}
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Exchange code for token using slack-go library
	tokenResp, ierr := s.exchangeSlackToken(clientID, clientSecret, redirectURL, code)
	if ierr != nil {
		return nil, ierr
	}

	// Get user info from Slack using the access token
	slackUser, ierr := s.getSlackUserInfo(tokenResp.IdToken)
	if ierr != nil {
		return nil, ierr
	}

	return slackUser, nil
}

func (s authService) exchangeSlackToken(clientID, clientSecret, redirectURL, code string) (*slack.OpenIDConnectResponse, core.IError) {
	// Use slack-go library for OAuth token exchange
	resp, err := slack.GetOpenIDConnectToken(http.DefaultClient, clientID, clientSecret, code, redirectURL)
	if err != nil {
		ierr := core.Error{
			Status:  http.StatusBadRequest,
			Code:    "SLACK_TOKEN_EXCHANGE_FAILED",
			Message: "Failed to exchange Slack authorization code: " + err.Error(),
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	return resp, nil
}

func (s authService) getSlackUserInfo(tokenID string) (*SlackUser, core.IError) {
	token, _, err := new(jwt.Parser).ParseUnverified(tokenID, jwt.MapClaims{})
	if err != nil {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_USER_INFO_FAILED",
			Message: "Failed to get user info from Slack: " + err.Error(),
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_USER_INFO_FAILED",
			Message: "Failed to get user info from Slack: invalid token claims",
		}
		return nil, s.ctx.NewError(ierr, ierr)
	}

	slackUser := &SlackUser{
		ID:          claims["sub"].(string),
		Email:       claims["email"].(string),
		DisplayName: cases.Title(language.Und).String(strings.Split(claims["email"].(string), "@")[0]),
		FullName:    claims["name"].(string),
		Image:       claims["picture"].(string),
		Company:     claims["https://slack.com/team_name"].(string),
	}

	return slackUser, nil
}

func (s authService) findOrCreateSlackUser(slackUser *SlackUser) (*models.User, core.IError) {
	initialPermissions := &models.UserAccessLevel{
		Clockin:   models.UserPermissionLevelUser,
		Timesheet: models.UserPermissionLevelUser,
		Pmo:       models.UserPermissionLevelNone,
		Setting:   models.UserPermissionLevelNone,
	}
	// Try to find existing user by email
	user, ierr := repo.User(s.ctx, repo.UserWithAllRelation()).FindOne("email = ?", slackUser.Email)
	if ierr != nil {
		if !errmsgs.IsNotFoundError(ierr) {
			return nil, s.ctx.NewError(ierr, ierr)
		}

		// Create new user
		newUser := &models.User{
			BaseModelHardDelete: models.NewBaseModelHardDelete(),
			Email:               slackUser.Email,
			FullName:            slackUser.FullName,
			DisplayName:         slackUser.DisplayName,
			AvatarURL:           slackUser.Image,
			Company:             utils.ToPointer(slackUser.Company),
			SlackID:             utils.ToPointer(slackUser.ID),
			IsActive:            true,
		}

		ierr = repo.User(s.ctx).Create(newUser)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}

		initialPermissions.UserID = newUser.ID
		ierr = repository.New[models.UserAccessLevel](s.ctx).Create(initialPermissions)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}

		return newUser, nil

	}

	if !user.IsActive {
		ierr := core.Error{
			Status:  http.StatusUnauthorized,
			Code:    "USER_IS_DEACTIVATED",
			Message: "User is deactivated",
		}
		return nil, s.ctx.NewError(ierr, ierr)
	}

	user.AvatarURL = slackUser.Image
	user.FullName = slackUser.FullName
	user.Company = utils.ToPointer(slackUser.Company)
	user.SlackID = utils.ToPointer(slackUser.ID)

	ierr = repo.User(s.ctx).Where("id = ?", user.ID).Updates(user)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	if user.AccessLevel == nil {
		initialPermissions.UserID = user.ID
		ierr := repository.New[models.UserAccessLevel](s.ctx).Create(initialPermissions)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}
	}

	return user, nil
}

func NewAuthService(ctx core.IContext) IAuthService {
	return &authService{ctx: ctx}
}
