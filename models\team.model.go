package models

type Team struct {
	BaseModel
	Name           string  `json:"name" gorm:"column:name"`
	Code           string  `json:"code" gorm:"column:code"`
	Color          string  `json:"color" gorm:"column:color"`
	Description    *string `json:"description" gorm:"column:description"`
	WorkingStartAt *string `json:"working_start_at" gorm:"column:working_start_at;type:time"`
	WorkingEndAt   *string `json:"working_end_at" gorm:"column:working_end_at;type:time"`

	// Relations
	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	Users []User `json:"users" gorm:"foreignKey:TeamCode;references:Code"`
}

func (Team) TableName() string {
	return "teams"
}
