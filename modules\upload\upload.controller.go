package upload

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type UploadController struct{}

func (m UploadController) Upload(c core.IHTTPContext) error {
	fileHeader, err := c.FormFile("file")
	if err != nil {
		return c.JSON(http.StatusBadRequest, core.Map{
			"code":    "FILE_REQUIRED",
			"message": "file is required",
		})
	}

	uploadSvc := services.NewUploadService(c)
	res, ierr := uploadSvc.UploadFile(fileHeader)
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m UploadController) GetFile(c core.IHTTPContext) error {
	// Get the file path from the URL parameter
	filePath := c.Param("*")
	if filePath == "" {
		return c.JSON(http.StatusBadRequest, core.Map{
			"code":    "FILE_PATH_REQUIRED",
			"message": "file path is required",
		})
	}

	uploadSvc := services.NewUploadService(c)
	fileReader, contentType, ierr := uploadSvc.GetFile(filePath)
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}
	defer fileReader.Close()

	// Set appropriate headers
	c.Response().Header().Set("Content-Type", contentType)

	// Stream the file content
	return c.Stream(http.StatusOK, contentType, fileReader)
}
