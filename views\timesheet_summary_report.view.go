package views

import (
	"encoding/json"
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/idin-core/utils"
)

type TimesheetSummaryReportProjectTiming struct {
	ProjectCode *string `json:"project_code"`
	ProjectName *string `json:"project_name"`
	SgaName     *string `json:"sga_name"`
	Type        string  `json:"type"`
	TotalTiming float64 `json:"total_timing"`
}

type TimesheetSummaryReportView struct {
	*models.User
	TotalTiming         float64                               `json:"total_timing"`
	TotalProjectTiming  float64                               `json:"total_project_timing"`
	TotalLeaveTiming    float64                               `json:"total_leave_timing"`
	TotalSgaTiming      float64                               `json:"total_sga_timing"`
	TotalInternalTiming float64                               `json:"total_internal_timing"`
	TotalExternalTiming float64                               `json:"total_external_timing"`
	TotalOtTiming       float64                               `json:"total_ot_timing"`
	Timings             []TimesheetSummaryReportProjectTiming `json:"timings"`
}

func (v TimesheetSummaryReportView) MarshalJSON() ([]byte, error) {
	// Handle the case where User is nil
	if v.User == nil {
		return json.Marshal(struct {
			TotalTiming         float64                               `json:"total_timing"`
			TotalProjectTiming  float64                               `json:"total_project_timing"`
			TotalLeaveTiming    float64                               `json:"total_leave_timing"`
			TotalSgaTiming      float64                               `json:"total_sga_timing"`
			TotalInternalTiming float64                               `json:"total_internal_timing"`
			TotalExternalTiming float64                               `json:"total_external_timing"`
			TotalOtTiming       float64                               `json:"total_ot_timing"`
			Timings             []TimesheetSummaryReportProjectTiming `json:"timings"`
		}{
			TotalTiming:         v.TotalTiming,
			TotalProjectTiming:  v.TotalProjectTiming,
			TotalLeaveTiming:    v.TotalLeaveTiming,
			TotalSgaTiming:      v.TotalSgaTiming,
			TotalInternalTiming: v.TotalInternalTiming,
			TotalExternalTiming: v.TotalExternalTiming,
			TotalOtTiming:       v.TotalOtTiming,
			Timings:             v.Timings,
		})
	}

	// Format joined_date as string like the User model does
	var joinedDateStr *string
	if v.User.JoinedDate != nil {
		joinedDateStr = utils.ToPointer(v.User.JoinedDate.Format(time.DateOnly))
	}

	// Create a combined struct with all fields
	return json.Marshal(struct {
		ID                  string                                `json:"id"`
		Email               string                                `json:"email"`
		FullName            string                                `json:"full_name"`
		DisplayName         string                                `json:"display_name"`
		Position            string                                `json:"position"`
		TeamCode            *string                               `json:"team_code"`
		Company             *string                               `json:"company"`
		AvatarURL           string                                `json:"avatar_url"`
		SlackID             *string                               `json:"slack_id"`
		IsActive            bool                                  `json:"is_active"`
		JoinedDate          *string                               `json:"joined_date"`
		CreatedAt           *time.Time                            `json:"created_at"`
		UpdatedAt           *time.Time                            `json:"updated_at"`
		TotalTiming         float64                               `json:"total_timing"`
		TotalProjectTiming  float64                               `json:"total_project_timing"`
		TotalLeaveTiming    float64                               `json:"total_leave_timing"`
		TotalSgaTiming      float64                               `json:"total_sga_timing"`
		TotalInternalTiming float64                               `json:"total_internal_timing"`
		TotalExternalTiming float64                               `json:"total_external_timing"`
		TotalOtTiming       float64                               `json:"total_ot_timing"`
		Timings             []TimesheetSummaryReportProjectTiming `json:"timings"`
	}{
		ID:                  v.User.ID,
		Email:               v.User.Email,
		FullName:            v.User.FullName,
		DisplayName:         v.User.DisplayName,
		Position:            v.User.Position,
		TeamCode:            v.User.TeamCode,
		Company:             v.User.Company,
		AvatarURL:           v.User.AvatarURL,
		SlackID:             v.User.SlackID,
		IsActive:            v.User.IsActive,
		JoinedDate:          joinedDateStr,
		CreatedAt:           v.User.CreatedAt,
		UpdatedAt:           v.User.UpdatedAt,
		TotalTiming:         v.TotalTiming,
		TotalProjectTiming:  v.TotalProjectTiming,
		TotalLeaveTiming:    v.TotalLeaveTiming,
		TotalSgaTiming:      v.TotalSgaTiming,
		TotalInternalTiming: v.TotalInternalTiming,
		TotalExternalTiming: v.TotalExternalTiming,
		TotalOtTiming:       v.TotalOtTiming,
		Timings:             v.Timings,
	})
}
