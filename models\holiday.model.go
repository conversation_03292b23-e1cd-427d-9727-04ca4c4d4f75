package models

import "time"

type Holiday struct {
	BaseModelHardDelete
	Name        string    `json:"name" gorm:"column:name"`
	Date        time.Time `json:"date" gorm:"column:date;type:date"`
	CreatedByID *string   `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string   `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
}

func (Holiday) TableName() string {
	return "holidays"
}
