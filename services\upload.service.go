package services

import (
	"io"
	"mime/multipart"
	"path/filepath"
	"strings"

	"github.com/aws/aws-sdk-go/aws"
	ss3 "github.com/aws/aws-sdk-go/service/s3"
	"gitlab.finema.co/finema/finework/finework-api/consts"
	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/views"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IUploadService interface {
	UploadFile(file *multipart.FileHeader) (*views.File, core.IError)
	GetFile(path string) (io.ReadCloser, string, core.IError)
}

type uploadService struct {
	ctx core.IContext
}

// UploadInternal uploads a file internally using the provided file, content type, original file name, and file size.
func (s uploadService) UploadInternal(file io.ReadSeeker, contentType string, ogFileName string, fileSize int64) (*views.File, core.IError) {
	// Connect to S3
	s3, err := core.NewS3(s.ctx.ENV().Config()).Connect()
	if err != nil {
		return nil, s.ctx.NewError(err, emsgs.UploadS3ConnectError)
	}

	// Validate file extension
	ierr := s.filterFileExtension(ogFileName)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Generate file name
	fileName := s.getfileName(ogFileName)

	// Generate path using current date and file name
	path := utils.GetCurrentDateTime().Format("20060102") + "/" + fileName

	// Upload file to S3
	_, err = s3.PutObject(path, file, &ss3.PutObjectInput{
		ContentType: aws.String(contentType),
	}, nil)
	if err != nil {
		return nil, s.ctx.NewError(err, emsgs.UploadCannotUploadFile)
	}

	// Return the uploaded file information
	return &views.File{
		URL:         s.ctx.ENV().String(consts.EnvAPIURL) + "/storage/" + path,
		Path:        "/" + path,
		Name:        ogFileName,
		Size:        fileSize,
		ContentType: contentType,
	}, nil
}

// UploadFile handles multipart upload from HTTP request
func (s uploadService) UploadFile(fileHeader *multipart.FileHeader) (*views.File, core.IError) {
	src, err := fileHeader.Open()
	if err != nil {
		return nil, s.ctx.NewError(err, emsgs.UploadCannotUploadFile)
	}
	defer src.Close()

	// multipart.File implements io.ReadSeeker
	reader, ok := src.(io.ReadSeeker)
	if !ok {
		return nil, s.ctx.NewError(nil, emsgs.UploadInvalidFileStream)
	}

	contentType := fileHeader.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	return s.UploadInternal(reader, contentType, fileHeader.Filename, fileHeader.Size)
}

// GetFile retrieves a file from S3 storage
func (s uploadService) GetFile(path string) (io.ReadCloser, string, core.IError) {
	// Connect to S3
	s3, err := core.NewS3(s.ctx.ENV().Config()).Connect()
	if err != nil {
		return nil, "", s.ctx.NewError(err, emsgs.UploadS3ConnectError)
	}

	// Remove leading slash if present
	if strings.HasPrefix(path, "/") {
		path = path[1:]
	}

	// Get object from S3
	result, err := s3.GetObject(path, nil)
	if err != nil {
		return nil, "", s.ctx.NewError(err, emsgs.UploadFileNotFound)
	}

	// Get content type from S3 object metadata
	contentType := "application/octet-stream"
	if result.ContentType != nil {
		contentType = *result.ContentType
	}

	return result.Body, contentType, nil
}

// filterFileExtension validates allowed file extensions
func (s uploadService) filterFileExtension(filename string) core.IError {
	ext := strings.ToLower(strings.TrimPrefix(filepath.Ext(filename), "."))
	if ext == "" {
		return emsgs.UploadExtensionNotAllowed
	}
	allowed := map[string]bool{
		"jpg": true, "jpeg": true, "png": true, "gif": true, "webp": true,
		"pdf": true, "doc": true, "docx": true, "xls": true, "xlsx": true, "txt": true,
	}
	if !allowed[ext] {
		return emsgs.UploadExtensionNotAllowed
	}
	return nil
}

// getfileName generates a random file name preserving extension
func (s uploadService) getfileName(ogFileName string) string {
	ext := strings.ToLower(filepath.Ext(ogFileName))
	uid := strings.ReplaceAll(utils.GetUUID(), "-", "")
	return uid + ext
}

func NewUploadService(ctx core.IContext) IUploadService {
	return &uploadService{ctx: ctx}
}
