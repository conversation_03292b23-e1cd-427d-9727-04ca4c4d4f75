model departments {
  id String @id @default(uuid()) @db.Uuid

  ministry_id   String    @db.Uuid
  name_th       String    @unique
  name_en       String?   @unique
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  ministry ministries @relation(fields: [ministry_id], references: [id], onDelete: Cascade)

  @@index([ministry_id])
  @@index([name_th])
}
