package services

import (
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ICheckinService interface {
	Create(input *CheckinCreatePayload) ([]models.Checkin, core.IError)
	Find(id string) (*models.Checkin, core.IError)
	Pagination(pageOptions *core.PageOptions, options *CheckinPaginationOptions) (*repository.Pagination[models.Checkin], core.IError)
	OnThisDay(options *CheckinPaginationOptions) ([]models.Checkin, core.IError)
	Delete(id string) core.IError
}

type checkinService struct {
	ctx core.IContext
}

func (s checkinService) Create(input *CheckinCreatePayload) ([]models.Checkin, core.IError) {
	// Create a map to store FirstCheckinAt for each date
	dateFirstCheckinMap := make(map[string]*time.Time)

	// Process each item to find existing checkins and preserve FirstCheckinAt
	for _, item := range input.Items {
		if item.Date == nil {
			continue
		}

		dateStr := item.Date.Format(time.DateOnly)

		// Skip if we already processed this date
		if _, exists := dateFirstCheckinMap[dateStr]; exists {
			continue
		}

		// Find existing checkins for this date
		existingCheckins, ierr := repo.Checkin(s.ctx).
			Where("user_id = ? AND DATE(date) = ?", input.UserId, dateStr).
			Order("date ASC").
			FindAll()

		if ierr != nil && !errmsgs.IsNotFoundError(ierr) {
			return nil, s.ctx.NewError(ierr, ierr)
		}

		// Store the FirstCheckinAt for this date
		if len(existingCheckins) > 0 {
			dateFirstCheckinMap[dateStr] = existingCheckins[0].Date
		} else {
			dateFirstCheckinMap[dateStr] = utils.GetCurrentDateTime()
		}

		// Mark existing checkins as unused for this date
		ierr = s.updateUnusedByUserAndDate(input.UserId, item.Date)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}
	}

	// Create new checkin records
	checkins := []models.Checkin{}
	for _, item := range input.Items {
		// Get the FirstCheckinAt for this item's date
		var firstCheckinAt *time.Time
		if item.Date != nil {
			dateStr := item.Date.Format(time.DateOnly)
			if storedTime, exists := dateFirstCheckinMap[dateStr]; exists {
				firstCheckinAt = storedTime
			} else {
				firstCheckinAt = utils.GetCurrentDateTime()
			}
		} else {
			firstCheckinAt = utils.GetCurrentDateTime()
		}

		checkin := models.Checkin{
			BaseModelHardDelete: models.NewBaseModelHardDelete(),
			UserId:              input.UserId,
			Type:                models.CheckinType(item.Type),
			Period:              models.CheckinPeriod(item.Period),
			Location:            item.Location,
			Remarks:             item.Remarks,
			IsUnused:            item.IsUnused,
			Date:                item.Date,
			FirstCheckinAt:      firstCheckinAt,
		}

		// Set LeaveType if provided
		if item.LeaveType != nil {
			checkin.LeaveType = utils.ToPointer(models.CheckinLeaveType(utils.ToNonPointer(item.LeaveType)))
		}

		checkins = append(checkins, checkin)
	}

	// Create the new checkins in the database
	ierr := repo.Checkin(s.ctx).Create(checkins)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return checkins, nil
}

func (s checkinService) Find(id string) (*models.Checkin, core.IError) {
	return repo.Checkin(s.ctx, repo.CheckinWithAllRelation()).FindOne("id = ?", id)
}

func (s checkinService) Pagination(pageOptions *core.PageOptions, options *CheckinPaginationOptions) (*repository.Pagination[models.Checkin], core.IError) {
	return repo.Checkin(
		s.ctx,
		repo.CheckinWithAllRelation(),
		repo.CheckinWithUser(options.UserID),
		repo.CheckinWithDateRange(options.StartDate, options.EndDate),
		repo.CheckinWithTeamCode(options.TeamCode),
		repo.CheckinWithIsUnused(options.IsUnused),
		repo.CheckinWithType(options.Type),
		repo.CheckinOrderBy(pageOptions)).
		Pagination(pageOptions)
}

func (s checkinService) OnThisDay(options *CheckinPaginationOptions) ([]models.Checkin, core.IError) {
	return repo.Checkin(
		s.ctx,
		repo.CheckinWithAllRelation(),
		repo.CheckinWithUser(options.UserID),
		repo.CheckinWithDateRange(options.StartDate, options.EndDate),
		repo.CheckinWithTeamCode(options.TeamCode),
		repo.CheckinWithIsUnused(utils.ToPointer(false)),
		repo.CheckinWithType(options.Type)).
		Order("created_at DESC").
		FindAll()
}

func (s checkinService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Checkin(s.ctx).Delete("id = ?", id)
}

// Helper method to find existing checkin by user ID and date
func (s checkinService) updateUnusedByUserAndDate(userId string, date *time.Time) core.IError {
	if date == nil {
		return nil
	}

	// Format date to compare only the date part (without time)
	dateStr := date.Format("2006-01-02")
	return repo.Checkin(s.ctx).Where("user_id = ? AND DATE(date) = ?", userId, dateStr).Updates(map[string]interface{}{
		"is_unused": true,
	})
}

func NewCheckinService(ctx core.IContext) ICheckinService {
	return &checkinService{ctx: ctx}
}
