package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ProjectUpdate struct {
	core.BaseValidator
	Name        *string `json:"name"`
	Code        *string `json:"code"`
	Description *string `json:"description"`
}

func (r *ProjectUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldCode := ""
	oldName := ""
	project, _ := repo.Project(cc).FindOne("id = ?", cc.Param("id"))
	if project != nil {
		oldCode = project.Code
		oldName = project.Name
	}

	if r.Code != nil {
		codeValue := utils.ToNonPointer(r.Code)
		if codeValue != strings.ToUpper(codeValue) {
			r.Must(false, &core.IValidMessage{
				Name:    "code",
				Code:    "UPPERCASE_REQUIRED",
				Message: "Code must be uppercase",
			})
		}
		if strings.Contains(codeValue, " ") {
			r.Must(false, &core.IValidMessage{
				Name:    "code",
				Code:    "NO_SPACES_ALLOWED",
				Message: "Code must not contain spaces",
			})
		}
	}

	r.Must(r.IsStrUnique(ctx, r.Name, models.Team{}.TableName(), "name", oldName, "name"))
	r.Must(r.IsStrUnique(ctx, r.Code, models.Team{}.TableName(), "code", oldCode, "code"))

	return r.Error()
}
