package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var Ministry = repository.Make[models.Ministry]()

func MinistryOrderBy(pageOptions *core.PageOptions) repository.Option[models.Ministry] {
	return func(c repository.IRepository[models.Ministry]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name_th ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func MinistryWithDepartments() repository.Option[models.Ministry] {
	return func(c repository.IRepository[models.Ministry]) {
		c.Preload("Departments")
	}
}

func MinistryWithSearch(q string) repository.Option[models.Ministry] {
	return func(c repository.IRepository[models.Ministry]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name_th ILIKE ? OR name_en ILIKE ?", searchTerm, searchTerm)
	}
}
