package me

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type MeController struct {
}

func (m MeController) GetProfile(c core.IHTTPContext) error {
	userID := c.GetUser().ID

	meSvc := services.NewMeService(c)
	user, err := meSvc.GetProfile(userID)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, user)
}

func (m MeController) UpdateProfile(c core.IHTTPContext) error {
	userID := c.GetUser().ID

	input := &requests.MeUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.<PERSON>SO<PERSON>(err.GetStatus(), err.JSO<PERSON>())
	}

	meSvc := services.NewMeService(c)
	payload := &services.MeUpdatePayload{}
	_ = utils.Copy(payload, input)

	user, err := meSvc.UpdateProfile(userID, payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, user)
}

func (m MeController) GetDevices(c core.IHTTPContext) error {
	userID := c.GetUser().ID

	meSvc := services.NewMeService(c)
	devices, err := meSvc.GetDevices(userID, c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, devices)
}

func (m MeController) DeleteDevice(c core.IHTTPContext) error {
	userID := c.GetUser().ID
	tokenID := c.Param("id")

	meSvc := services.NewMeService(c)
	err := meSvc.DeleteDevice(userID, tokenID)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
