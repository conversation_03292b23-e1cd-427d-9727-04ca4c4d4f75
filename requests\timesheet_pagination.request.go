package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type TimesheetPaginationRequest struct {
	core.BaseValidator
	StartDate   *string `json:"start_date" query:"start_date"`
	EndDate     *string `json:"end_date" query:"end_date"`
	UserID      *string `json:"user_id" query:"user_id"`
	ProjectCode *string `json:"project_code" query:"project_code"`
	TeamCode    *string `json:"team_code" query:"team_code"`
	SgaID       *string `json:"sga_id" query:"sga_id"`
	Type        *string `json:"type" query:"type"`
}

func (r *TimesheetPaginationRequest) Validate(ctx core.IContext) core.IError {
	r.Must(r.IsDate(r.StartDate, "start_date"))
	r.Must(r.IsDate(r.EndDate, "end_date"))

	// Validate user_id exists if provided
	if r.UserID != nil {
		r.Must(r.IsExists(ctx, r.UserID, models.User{}.TableName(), "id", "user_id"))
	}

	if r.Type != nil {
		r.Must(r.IsStrIn(r.Type, strings.Join([]string{
			string(models.TimesheetTypeExternal),
			string(models.TimesheetTypeInternal),
			string(models.TimesheetTypeLeave),
			string(models.TimesheetTypeSga),
			string(models.TimesheetTypeOt),
			string(models.TimesheetTypeProject),
		}, "|"), "type"))
	}

	if r.SgaID != nil {
		r.Must(r.IsExists(ctx, r.SgaID, models.Sga{}.TableName(), "id", "sga_id"))
	}

	if r.ProjectCode != nil {
		r.Must(r.IsExists(ctx, r.ProjectCode, models.Project{}.TableName(), "code", "project_code"))
	}
	if r.TeamCode != nil {
		r.Must(r.IsExists(ctx, r.TeamCode, models.Team{}.TableName(), "code", "team_code"))
	}

	return r.Error()
}
