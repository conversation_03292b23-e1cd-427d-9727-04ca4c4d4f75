package team

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type TeamController struct {
}

func (m TeamController) Pagination(c core.IHTTPContext) error {
	teamSvc := services.NewTeamService(c)
	res, ierr := teamSvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m TeamController) Find(c core.IHTTPContext) error {
	teamSvc := services.NewTeamService(c)
	team, err := teamSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.<PERSON><PERSON>tatus(), err.<PERSON><PERSON><PERSON>())
	}

	return c.JSON(http.StatusOK, team)
}

func (m TeamController) FindByCode(c core.IHTTPContext) error {
	teamSvc := services.NewTeamService(c)
	team, err := teamSvc.FindByCode(c.Param("code"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, team)
}

func (m TeamController) Create(c core.IHTTPContext) error {
	input := &requests.TeamCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	teamSvc := services.NewTeamService(c)
	payload := &services.TeamCreatePayload{}
	_ = utils.Copy(payload, input)
	team, err := teamSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, team)
}

func (m TeamController) Update(c core.IHTTPContext) error {
	input := &requests.TeamUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	teamSvc := services.NewTeamService(c)
	payload := &services.TeamUpdatePayload{}
	_ = utils.Copy(payload, input)
	team, err := teamSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, team)
}

func (m TeamController) Delete(c core.IHTTPContext) error {
	teamSvc := services.NewTeamService(c)
	err := teamSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
