package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type DepartmentCreate struct {
	core.BaseValidator
	MinistryID *string `json:"ministry_id"`
	NameTh     *string `json:"name_th"`
	NameEn     *string `json:"name_en"`
}

func (r *DepartmentCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.MinistryID, "ministry_id"))
	r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	
	r.Must(r.<PERSON>tr<PERSON>equired(r.NameTh, "name_th"))
	r.Must(r.Is<PERSON>trUnique(ctx, r.NameTh, models.Department{}.TableName(), "name_th", "", "name_th"))
	
	if utils.ToNonPointer(r.<PERSON>En) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameEn, models.Department{}.TableName(), "name_en", "", "name_en"))
	}

	return r.<PERSON><PERSON>r()
}
