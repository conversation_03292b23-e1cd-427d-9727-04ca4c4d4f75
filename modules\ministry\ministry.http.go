package ministry

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewMinistryHTTP(e *echo.Echo) {
	ministry := &MinistryController{}
	e.GET("/ministries", core.WithHTTPContext(ministry.Pagination), middleware.AuthMiddleware())
	e.GET("/ministries/:id", core.WithHTTPContext(ministry.Find), middleware.AuthMiddleware())

	e.POST("/admin/ministries", core.WithHTTPContext(ministry.Create), middleware.AuthMiddleware())
	e.PUT("/admin/ministries/:id", core.WithHTTPContext(ministry.Update), middleware.AuthMiddleware())
	e.DELETE("/admin/ministries/:id", core.WithHTTPContext(ministry.Delete), middleware.AuthMiddleware())
}
