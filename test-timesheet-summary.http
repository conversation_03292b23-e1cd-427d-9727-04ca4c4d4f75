### Test Timesheet Summary Report API
### This file tests the timesheet summary report functionality with the new database design

### Variables
@baseUrl = http://localhost:3000
@token = YOUR_AUTH_TOKEN_HERE

### ===== TIMESHEET SUMMARY REPORT TESTS =====

### 1. Get timesheet summary report for all teams
GET {{baseUrl}}/admin/timesheets/summary-report
Authorization: Bearer {{token}}

### 2. Get timesheet summary report with date range
GET {{baseUrl}}/admin/timesheets/summary-report?start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{token}}

### 3. Get timesheet summary report for specific team
GET {{baseUrl}}/admin/timesheets/summary-report?team_code=DEV
Authorization: Bearer {{token}}

### 4. Get timesheet summary report for specific team with date range
GET {{baseUrl}}/admin/timesheets/summary-report?team_code=DEV&start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{token}}

### 5. Test with invalid date format (should return validation error)
GET {{baseUrl}}/admin/timesheets/summary-report?start_date=invalid-date
Authorization: Bearer {{token}}

### 6. Test with invalid team code (should return validation error)
GET {{baseUrl}}/admin/timesheets/summary-report?team_code=INVALID_TEAM
Authorization: Bearer {{token}}

### ===== EXPECTED RESPONSE FORMAT =====
### The response should contain an array of users with their timesheet summaries:
### [
###   {
###     "id": "user-uuid",
###     "email": "<EMAIL>",
###     "full_name": "User Name",
###     "display_name": "Display Name",
###     "position": "Position",
###     "team_code": "TEAM",
###     "total_timing": 160.0,
###     "total_project_timing": 120.0,
###     "total_leave_timing": 8.0,
###     "total_sga_timing": 16.0,
###     "total_internal_timing": 8.0,
###     "total_external_timing": 4.0,
###     "total_ot_timing": 4.0,
###     "timings": [
###       {
###         "project_code": "PROJ001",
###         "project_name": "Project Name",
###         "sga_name": null,
###         "type": "PROJECT",
###         "total_timing": 40.0
###       },
###       {
###         "project_code": null,
###         "project_name": null,
###         "sga_name": "SGA Name",
###         "type": "SGA",
###         "total_timing": 16.0
###       }
###     ]
###   }
### ]

### ===== NOTES =====
### - The new database design stores project_name and sga_name directly in the timesheet table
### - No longer relies on JOIN operations with project and sga tables for the summary report
### - Improved performance by using denormalized data
### - Proper aggregation by project_code/sga_id and type
### - Correct calculation of total_project_timing for all PROJECT type timesheets
